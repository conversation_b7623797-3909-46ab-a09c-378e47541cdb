<template>
  <view class="container">
    <!-- 顶部定位和搜索栏 -->
    <view
      class="header flex items-center justify-between px-30rpx py-16rpx from-primary-50 to-white"
    >
      <view class="location flex items-center" @tap="openCityPicker">
        <text class="text-32rpx font-medium mr-8rpx">{{ currentCity }}</text>
        <text class="i-carbon-chevron-down text-24rpx color-info"></text>
      </view>

      <view
        class="search-box w-[65%] flex items-center bg-white rounded-full px-30rpx py-16rpx"
        @tap="toSearch"
      >
        <text class="i-carbon-search mr-10rpx color-grey"></text>
        <text class="color-grey text-28rpx">{{ searchPlaceholder }}</text>
      </view>

      <view class="ml-20rpx relative">
        <text class="i-carbon-notification text-40rpx"></text>
        <view v-if="hasNotification" class="notification-dot"></view>
      </view>
    </view>

    <!-- 核心功能导航区 -->
    <view
      class="core-nav py-30rpx px-20rpx mb-20rpx rounded-lg mx-16rpx mt-16rpx"
    >
      <view class="flex justify-between">
        <view
          v-for="(item, index) in coreNavItems"
          :key="index"
          class="nav-item flex flex-col items-center"
          @tap="navigateTo(item.linkPath)"
        >
          <image
            :src="item.path"
            mode="widthFix"
            class="w-92rpx h-92rpx mb-10rpx"
          />
          <text class="text-28rpx font-medium color-main">{{
            item.title
          }}</text>
        </view>
      </view>
    </view>

    <!-- 服务分类横向滚动列表 -->
    <view
      class="services-scroll bg-white px-20rpx py-30rpx mb-20rpx rounded-lg mx-16rpx"
    >
      <uv-scroll-list
        :indicator="true"
        indicator-color="#f0f0f0"
        indicator-active-color="#ff6d00"
        indicator-width="60"
        indicator-bar-width="20"
        :indicator-style="{ bottom: '10rpx' }"
      >
        <view class="services-container">
          <view
            v-for="(service, index) in services"
            :key="index"
            class="service-item flex flex-col items-center mr-40rpx"
            @tap="navigateTo(service.path)"
          >
            <view
              class="service-icon-bg flex items-center justify-center mb-10rpx"
              :class="index % 2 === 0 ? 'bg-primary-light' : 'bg-primary'"
            >
              <view :class="service.icon" class="text-48rpx color-white"></view>
            </view>
            <text class="text-24rpx color-main text-center">{{
              service.name
            }}</text>
            <view v-if="service.isNew" class="new-tag">新</view>
          </view>
        </view>
      </uv-scroll-list>
    </view>

    <!-- 今日必抢 -->
    <view class="must-buy-container bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">今日必抢</text>
        <view class="flex items-center" @tap="navigateTo('/pages/promo/deals')">
          <text class="text-26rpx color-grey">更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>
      <scroll-view scroll-x class="must-buy-scroll" show-scrollbar="false">
        <view class="flex px-20rpx pb-20rpx">
          <view
            v-for="(item, index) in mustBuyItems"
            :key="index"
            class="must-buy-item mr-20rpx rounded-lg overflow-hidden"
            @tap="navigateTo(item.link)"
          >
            <image :src="item.image" mode="aspectFill" class="item-image" />
            <view class="item-info p-16rpx">
              <text class="item-title text-28rpx color-main line-clamp-2">{{
                item.title
              }}</text>
              <view class="flex justify-between items-center mt-10rpx">
                <view class="flex items-baseline">
                  <text class="text-primary text-32rpx font-bold"
                    >¥{{ item.price }}</text
                  >
                  <text class="color-grey text-24rpx line-through ml-10rpx"
                    >¥{{ item.originalPrice }}</text
                  >
                </view>
                <view class="buy-btn">抢</view>
              </view>
              <text class="color-grey text-24rpx"
                >已抢{{ item.soldCount }}</text
              >
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 附近推荐 -->
    <view class="nearby-container bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">附近推荐</text>
        <view
          class="flex items-center"
          @tap="navigateTo('/pages/nearby/index')"
        >
          <text class="text-26rpx color-grey">更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 分类标签 -->
      <scroll-view scroll-x class="category-tabs" show-scrollbar="false">
        <view class="flex py-10rpx px-20rpx">
          <view
            v-for="(category, index) in categories"
            :key="index"
            class="category-item px-20rpx py-10rpx mr-20rpx rounded-full"
            :class="
              category.active
                ? 'bg-primary-50 text-primary'
                : 'bg-gray-100 color-info'
            "
            @tap="switchCategory(index)"
          >
            {{ category.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 推荐列表 -->
      <view class="nearby-list px-20rpx pb-20rpx">
        <view
          v-for="(item, index) in recommendItems"
          :key="index"
          class="nearby-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
          @tap="navigateTo(item.link)"
        >
          <image
            :src="item.image"
            mode="aspectFill"
            class="nearby-image rounded-lg"
          />
          <view class="nearby-info flex-1 ml-20rpx">
            <view class="flex items-center">
              <text class="nearby-title text-30rpx font-bold line-clamp-2">{{
                item.title
              }}</text>
              <text v-if="item.adTag" class="ad-tag ml-10rpx">广告</text>
            </view>
            <text class="color-grey text-26rpx">距离{{ item.distance }}</text>
            <view class="flex items-center mt-10rpx">
              <view class="flex items-baseline">
                <text class="text-primary text-32rpx font-bold"
                  >¥{{ item.price }}</text
                >
                <text class="color-grey text-24rpx line-through ml-10rpx"
                  >¥{{ item.originalPrice }}</text
                >
              </view>
              <view v-if="item.memberDiscount" class="member-tag ml-10rpx"
                >会员减{{ item.memberDiscount }}</view
              >
            </view>
            <view class="flex justify-between mt-10rpx">
              <text class="color-grey text-24rpx"
                >{{ item.peopleCount }}人使用</text
              >
              <view v-if="item.rating" class="rating text-primary"
                >{{ item.rating }}分</view
              >
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 招聘推荐 -->
    <view class="job-container bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">热门招聘</text>
        <view class="flex items-center" @tap="navigateTo('/pages/job/index')">
          <text class="text-26rpx color-grey">全部</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>
      <view class="job-list px-20rpx pb-20rpx">
        <view
          v-for="(job, index) in jobList"
          :key="index"
          class="job-item p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
          @tap="navigateTo(job.link)"
        >
          <view class="flex justify-between items-start">
            <view>
              <text class="text-32rpx font-bold color-main">{{
                job.title
              }}</text>
              <view class="flex items-center mt-10rpx">
                <text class="text-36rpx font-bold text-primary">{{
                  job.salary
                }}</text>
              </view>
              <view class="job-tags flex flex-wrap mt-10rpx">
                <view
                  v-for="(tag, tagIndex) in job.tags"
                  :key="tagIndex"
                  class="job-tag mr-10rpx mb-10rpx"
                >
                  {{ tag }}
                </view>
              </view>
              <view class="flex items-center mt-10rpx">
                <text class="color-grey text-26rpx">{{ job.company }}</text>
                <text class="color-grey text-26rpx ml-20rpx">{{
                  job.location
                }}</text>
              </view>
            </view>
            <image
              :src="job.logo"
              mode="aspectFill"
              class="job-logo rounded-lg"
            ></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 房产推荐 -->
    <view class="house-container bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">热门房源</text>
        <view
          class="flex items-center"
          @tap="navigateTo('/pages/house/secondHouse/index')"
        >
          <text class="text-26rpx color-grey">全部</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>
      <view class="house-list px-20rpx pb-20rpx">
        <view
          v-for="(house, index) in houseList"
          :key="index"
          class="house-item mb-20rpx bg-white rounded-lg shadow-sm overflow-hidden"
          @tap="navigateTo(house.link)"
        >
          <image
            :src="house.image"
            mode="aspectFill"
            class="house-image w-full"
          ></image>
          <view class="p-20rpx">
            <text class="text-30rpx font-bold color-main line-clamp-1">{{
              house.title
            }}</text>
            <view class="flex mt-10rpx">
              <view
                v-for="(tag, tagIndex) in house.tags"
                :key="tagIndex"
                class="house-tag mr-10rpx"
              >
                {{ tag }}
              </view>
            </view>
            <view class="flex justify-between items-center mt-10rpx">
              <text class="text-36rpx font-bold text-primary">{{
                house.price
              }}</text>
              <text class="color-grey text-26rpx"
                >{{ house.area }} | {{ house.type }}</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { assetsSvg } from "@/utils/assetsUtil";
// 城市定位
const currentCity = ref("北京");
const searchPlaceholder = ref("搜索商家");

// 是否有未读通知
const hasNotification = ref(true);

// 获取主题色（暂时保留，后续可能会用到）
// const primaryColor = ref("#ff6d00");
// const primaryBgColor = ref("#ff6d00");

// 核心导航项
const coreNavItems = [
  {
    title: "求职招聘",
    icon: "i-carbon-user-profile",
    path: assetsSvg.recruiting,
    linkPath: "/pages/job/index",
  },
  {
    title: "二手房",
    icon: "i-carbon-building",
    path: assetsSvg.secondHandHouse,
    linkPath: "/pages/house/secondHouse/index",
  },
  {
    title: "租房",
    icon: "i-carbon-home",
    path: assetsSvg.renting,
    linkPath: "/pages/house/rent/index",
  },
  {
    title: "新房",
    icon: "i-carbon-apartment",
    path: assetsSvg.newHouse,
    linkPath: "/pages/house/newHouse/index",
  },
  {
    title: "交友",
    icon: "i-carbon-friendship",
    path: assetsSvg.dating,
    linkPath: "/pages/dating/index",
  },
];

// 服务分类九宫格数据
const services = [
  { name: "附近工作", icon: "i-carbon-location", path: "/pages/job/nearby" },
  { name: "维修服务", icon: "i-carbon-tools", path: "/pages/service/repair" },
  {
    name: "商铺写字楼",
    icon: "i-carbon-building-insights",
    path: "/pages/house/shop",
  },
  { name: "家庭保洁", icon: "i-carbon-clean", path: "/pages/service/clean" },
  {
    name: "新房",
    icon: "i-carbon-home",
    path: "/pages/house/newHouse/index",
    isNew: true,
  },
  {
    name: "家电维修",
    icon: "i-carbon-settings-adjust",
    path: "/pages/service/appliance",
  },
  { name: "附近钟点工", icon: "i-carbon-time", path: "/pages/service/hourly" },
  {
    name: "保姆月嫂",
    icon: "i-carbon-user-avatar",
    path: "/pages/service/nanny",
  },
  { name: "二手车", icon: "i-carbon-car", path: "/pages/car/used" },
  {
    name: "商家入驻",
    icon: "i-carbon-store",
    path: "/pages/merchant/join",
    isNew: true,
  },
  { name: "招聘会", icon: "i-carbon-event", path: "/pages/job/fair" },
  {
    name: "购物优惠",
    icon: "i-carbon-shopping-cart",
    path: "/pages/shopping/discount",
  },
  { name: "送水服务", icon: "i-carbon-delivery", path: "/pages/service/water" },
  {
    name: "家居装修",
    icon: "i-carbon-paint-brush",
    path: "/pages/service/decoration",
  },
  {
    name: "房产中介",
    icon: "i-carbon-partnership",
    path: "/pages/house/agent",
  },
];

// 今日必抢商品数据
const mustBuyItems = [
  {
    image: "https://picsum.photos/seed/must-buy-1/300/200",
    title: "卡夫卡少儿免费公开课",
    price: "29",
    originalPrice: "59",
    soldCount: "1000+",
    link: "/pages/promo/detail?id=1",
  },
  {
    image: "https://picsum.photos/seed/must-buy-2/300/200",
    title: "天然棉纱影婚情侣套餐",
    price: "9.9",
    originalPrice: "99",
    soldCount: "1000+",
    link: "/pages/promo/detail?id=2",
  },
  {
    image: "https://picsum.photos/seed/must-buy-3/300/200",
    title: "3天2晚包食宿云南大理洱海",
    price: "299",
    originalPrice: "1299",
    soldCount: "500+",
    link: "/pages/promo/detail?id=3",
  },
  {
    image: "https://picsum.photos/seed/must-buy-4/300/200",
    title: "儿童画画艺术培训课程",
    price: "19.9",
    originalPrice: "199",
    soldCount: "800+",
    link: "/pages/promo/detail?id=4",
  },
];

// 分类标签
const categories = [
  { name: "人气推荐", active: true },
  { name: "餐饮美食", active: false },
  { name: "休闲娱乐", active: false },
  { name: "优惠特惠", active: false },
  { name: "好评优先", active: false },
];

// 切换分类
const switchCategory = (index: number) => {
  categories.forEach((item, i) => {
    item.active = i === index;
  });
  // TODO: 根据分类加载不同数据
};

// 附近推荐列表
const recommendItems = [
  {
    image: "https://picsum.photos/seed/nearby-1/200/150",
    title: "3天2晚包食宿 云南大理-洱海-情侣双人套餐",
    distance: "1.2km",
    price: "299",
    originalPrice: "1299",
    memberDiscount: "¥100",
    peopleCount: "200+",
    adTag: true,
    link: "/pages/service/detail?id=1",
  },
  {
    image: "https://picsum.photos/seed/nearby-2/200/150",
    title: "卡夫卡少儿45分钟舞蹈免费公开课",
    distance: "3.3km",
    price: "29.9",
    originalPrice: "49.9",
    peopleCount: "100+",
    rating: "5.0",
    link: "/pages/service/detail?id=2",
  },
  {
    image: "https://picsum.photos/seed/nearby-3/200/150",
    title: "竹林烤肉季 烤肉套餐4-6人餐",
    distance: "500m",
    price: "139",
    originalPrice: "239",
    peopleCount: "300+",
    rating: "4.8",
    link: "/pages/service/detail?id=3",
  },
];

// 热门招聘列表
const jobList = [
  {
    title: "招聘外卖员加入我们成为团队一员",
    salary: "8500-15000元",
    company: "辽宁百嘉企业管理咨询有限公司",
    location: "海淀·清河",
    tags: ["包住", "包吃", "加班补助", "饭补"],
    logo: "https://picsum.photos/seed/comp1/100/100",
    link: "/pages/job/detail?id=1",
  },
  {
    title: "踏实干月入过万骑手超能赚",
    salary: "8000-14000元",
    company: "辽宁百嘉企业管理咨询有限公司",
    location: "海淀·清河",
    tags: ["包住", "饭补", "话补", "房补", "包吃"],
    logo: "https://picsum.photos/seed/comp2/100/100",
    link: "/pages/job/detail?id=2",
  },
  {
    title: "昌平美团骑手能预支",
    salary: "9000-15000元",
    company: "天津吉城美家生活服务有限公司",
    location: "北京·昌平",
    tags: ["无需经验", "当天入职", "包住宿"],
    logo: "https://picsum.photos/seed/comp3/100/100",
    link: "/pages/job/detail?id=3",
  },
];

// 热门房源列表
const houseList = [
  {
    title: "西二旗地铁站附近精装三居室 南北通透",
    image: "https://picsum.photos/seed/house1/700/400",
    price: "350万",
    area: "89㎡",
    type: "3室1厅",
    tags: ["满五年", "地铁房", "精装"],
    link: "/pages/house/detail?id=1",
  },
  {
    title: "朝阳大悦城商圈 精装修现房 拎包入住",
    image: "https://picsum.photos/seed/house2/700/400",
    price: "580万",
    area: "120㎡",
    type: "4室2厅",
    tags: ["学区房", "南北通透", "电梯房"],
    link: "/pages/house/detail?id=2",
  },
];

// 打开城市选择器
const openCityPicker = () => {
  uni.showToast({
    title: "城市选择功能开发中",
    icon: "none",
  });
};

// 跳转到搜索页面
const toSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

// 导航到指定页面
const navigateTo = (path: string) => {
  if (!path) return;
  uni.navigateTo({
    url: path,
  });
};

// 显示全部服务（暂时保留，后续可能会用到）
// const showAllServices = () => {
//   uni.showToast({
//     title: "全部服务功能开发中",
//     icon: "none",
//   });
// };
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// .header {
// }

.location {
  max-width: 25%;
}

// 核心导航区
.icon-circle {
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s;
  }

  &:active::after {
    transform: scale(1);
  }
}

.bg-gradient-0 {
  background: linear-gradient(135deg, #ff9a9e, #ff6b6b);
}

.bg-gradient-1 {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.bg-gradient-2 {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.bg-gradient-3 {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.bg-gradient-4 {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
}

// 服务分类横向滚动列表
.services-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 10rpx 0;
}

.service-item {
  position: relative;
  min-width: 120rpx;
  flex-shrink: 0;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.95);
  }

  &:last-child {
    margin-right: 20rpx;
  }
}

.service-icon-bg {
  width: 88rpx;
  height: 88rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;

  &:active {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  }
}

.bg-primary-light {
  background: linear-gradient(135deg, #ff9a9e, #ff6b6b);
}

.bg-primary {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.new-tag {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: $primary;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  z-index: 1;
}

// 今日必抢
.must-buy-scroll {
  white-space: nowrap;
}

.must-buy-item {
  display: inline-block;
  width: 300rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.item-image {
  width: 300rpx;
  height: 200rpx;
}

.buy-btn {
  background-color: $primary;
  color: white;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

// 附近推荐
.category-tabs {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
}

.nearby-title {
  font-size: 32rpx;
  font-weight: 600;
}

.nearby-image {
  width: 220rpx;
  height: 160rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nearby-item {
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.ad-tag {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  border-radius: 6rpx;
}

.member-tag {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  border-radius: 6rpx;
}

// 热门招聘
.job-logo {
  width: 100rpx;
  height: 100rpx;
}

.job-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $text-info;
  border-radius: 6rpx;
}

// 热门房源
.house-image {
  height: 320rpx;
}

.house-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $text-info;
  border-radius: 6rpx;
}

.notification-dot {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: $primary;
  border-radius: 50%;
}

.text-primary {
  color: $primary;
}
</style>
