<template>
  <view class="dating-page">
    <!-- 顶部导航栏 -->
    <view class="navbar-container sticky top-0 z-10">
      <view class="navbar px-30rpx py-20rpx flex justify-between items-center bg-white shadow-sm">
        <view class="navbar-title text-36rpx font-bold">缘分空间</view>
        <view class="navbar-actions flex items-center">
          <view class="action-icon mx-15rpx">
            <text class="i-carbon-search text-44rpx"></text>
          </view>
          <view class="action-icon mx-15rpx position-relative">
            <text class="i-carbon-notification text-44rpx"></text>
            <view class="notification-badge"></view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="dating-scroll" refresher-enabled @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing">
      <!-- 广告轮播图 -->
      <view v-if="showBanner" class="banner-section px-30rpx py-20rpx">
        <swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
          indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#ff5778">
          <swiper-item v-for="(item, index) in bannerList" :key="index" @click="onBannerClick(item)">
            <image :src="item.image" mode="aspectFill" class="banner-image rounded-20rpx" />
            <view class="banner-overlay rounded-20rpx"></view>
            <view class="banner-content">
              <text class="banner-title">{{ item.title }}</text>
              <text class="banner-subtitle">{{ item.subtitle }}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 功能导航 -->
      <view class="features-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm">
        <view class="features-grid grid grid-cols-4 p-20rpx">
          <view v-for="(feature, index) in featureNavigations" :key="index"
            class="feature-item flex flex-col items-center py-10rpx" @click="navigateToFeature(feature.path)">
            <view :class="`feature-icon-bg bg-${feature.color}-100 mb-10rpx`">
              <text :class="`i-carbon-${feature.icon} text-${feature.color}-500 text-44rpx`"></text>
            </view>
            <text class="feature-name text-26rpx">{{ feature.name }}</text>
          </view>
        </view>
      </view>

      <!-- 精选活动 -->
      <view class="activities-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden">
        <view class="section-header px-30rpx py-20rpx flex justify-between items-center">
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">精选活动</text>
          </view>
          <view class="more-link flex items-center" @click="navigateTo('/pages/dating/activities')">
            <text class="text-26rpx color-primary">查看全部</text>
            <text class="i-carbon-chevron-right color-primary text-24rpx"></text>
          </view>
        </view>

        <scroll-view scroll-x class="activities-scroll hide-scrollbar px-30rpx pb-30rpx">
          <view class="flex">
            <view v-for="(activity, index) in datingActivities" :key="index" class="activity-card mr-25rpx"
              @click="viewActivityDetail(activity.id)">
              <view class="activity-image-container relative">
                <image :src="activity.image" mode="aspectFill" class="activity-image rounded-t-15rpx" />
                <view class="activity-status" :class="{ 'status-hot': activity.isHot, 'status-full': activity.isFull }">
                  {{ activity.isHot ? '火热招募中' : (activity.isFull ? '名额已满' : '') }}
                </view>
                <view class="activity-type-tag">{{ activity.type }}</view>
              </view>
              <view class="activity-content p-20rpx">
                <text class="activity-title text-30rpx font-semibold line-clamp-1">{{ activity.title }}</text>
                <view class="flex items-center mt-15rpx">
                  <text class="i-carbon-calendar color-grey text-28rpx mr-10rpx"></text>
                  <text class="activity-time text-26rpx color-grey">{{ activity.time }}</text>
                </view>
                <view class="flex items-center mt-10rpx">
                  <text class="i-carbon-location color-grey text-28rpx mr-10rpx"></text>
                  <text class="activity-location text-26rpx color-grey line-clamp-1">{{ activity.location }}</text>
                </view>
                <view class="flex justify-between items-center mt-15rpx">
                  <view class="flex items-baseline">
                    <text class="price text-primary text-34rpx font-bold">¥{{ activity.price }}</text>
                    <text v-if="activity.originalPrice"
                      class="original-price color-grey text-24rpx line-through ml-10rpx">¥{{ activity.originalPrice }}</text>
                  </view>
                  <view class="people-count-tag">
                    <text class="text-24rpx">{{ activity.currentCount }}/{{ activity.maxCount }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 猜你喜欢 -->
      <view class="recommendation-section mx-30rpx my-20rpx">
        <view class="section-header px-10rpx py-20rpx flex justify-between items-center">
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">猜你喜欢</text>
          </view>
          <view class="filter-tabs flex rounded-full bg-gray-100 p-6rpx">
            <view v-for="(tab, index) in filterTabs" :key="index"
              class="filter-tab px-20rpx py-10rpx rounded-full text-28rpx"
              :class="{ 'active-tab': activeTab === tab.value }" @click="setActiveTab(tab.value)">
              {{ tab.label }}
            </view>
          </view>
        </view>

        <!-- 用户卡片 - 大图滑动模式 -->
        <swiper class="user-cards-swiper" circular previous-margin="60rpx" next-margin="60rpx" @change="onSwiperChange">
          <swiper-item v-for="(user, index) in filteredUsers" :key="index" class="user-swiper-item">
            <view class="user-card-big" :class="{'card-active': currentCardIndex === index}">
              <view class="user-card-image-container relative">
                <image :src="user.avatar" mode="aspectFill" class="user-card-image" />
                <view class="user-card-gradient"></view>

                <view class="user-card-actions absolute bottom-30rpx left-0 right-0 flex justify-center">
                  <view class="action-btn action-dislike" @click.stop="dislikeUser(user.id)">
                    <text class="i-carbon-close-filled"></text>
                  </view>
                  <view class="action-btn action-like" @click.stop="likeUser(user.id)">
                    <text class="i-carbon-favorite-filled"></text>
                  </view>
                  <view class="action-btn action-message" @click.stop="messageUser(user.id)">
                    <text class="i-carbon-chat"></text>
                  </view>
                </view>

                <view v-if="user.isVip" class="vip-badge">
                  <text class="i-carbon-star-filled"></text>
                </view>

                <view v-if="user.isVerified" class="verified-badge">
                  <text class="i-carbon-checkmark mr-5rpx"></text>
                  <text>已认证</text>
                </view>
              </view>

              <view class="user-card-info p-25rpx">
                <view class="flex items-center mb-15rpx">
                  <text class="user-name text-36rpx font-bold mr-10rpx">{{ user.name }}</text>
                  <text class="user-age text-28rpx">{{ user.age }}岁</text>
                  <image v-if="user.gender === 'female'" src="/static/images/icon-female.png" class="gender-icon ml-10rpx" />
                  <image v-else src="/static/images/icon-male.png" class="gender-icon ml-10rpx" />
                </view>

                <view class="user-basic-info flex flex-wrap mb-20rpx">
                  <view class="info-item mr-20rpx">
                    <text class="i-carbon-education color-grey mr-5rpx"></text>
                    <text class="text-26rpx">{{ user.education }}</text>
                  </view>
                  <view class="info-item mr-20rpx">
                    <text class="i-carbon-portfolio color-grey mr-5rpx"></text>
                    <text class="text-26rpx">{{ user.job }}</text>
                  </view>
                  <view class="info-item">
                    <text class="i-carbon-location color-grey mr-5rpx"></text>
                    <text class="text-26rpx">{{ user.location }}</text>
                  </view>
                </view>

                <view class="user-tags flex flex-wrap mb-15rpx">
                  <view v-for="(tag, tagIndex) in user.tags" :key="tagIndex" class="user-tag">
                    {{ tag }}
                  </view>
                </view>

                <view class="user-intro text-26rpx color-grey line-clamp-2">
                  {{ user.introduction }}
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>

        <!-- 底部分页指示器 -->
        <view class="swiper-indicators flex justify-center mt-20rpx">
          <view v-for="(_, index) in filteredUsers" :key="index" class="indicator-dot"
            :class="{'active-dot': currentCardIndex === index}"></view>
        </view>
      </view>
      
      <!-- 约会攻略 -->
      <view class="dating-guides-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden">
        <view class="section-header px-30rpx py-20rpx flex justify-between items-center">
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">约会攻略</text>
          </view>
          <view class="more-link flex items-center" @click="navigateTo('/pages/dating/guides')">
            <text class="text-26rpx color-primary">更多攻略</text>
            <text class="i-carbon-chevron-right color-primary text-24rpx"></text>
          </view>
        </view>

        <view class="guides-list px-30rpx pb-30rpx">
          <view v-for="(guide, index) in datingGuides" :key="index"
            class="guide-item flex p-20rpx mb-20rpx bg-white rounded-15rpx shadow-sm"
            @click="viewGuideDetail(guide.id)">
            <image :src="guide.image" mode="aspectFill" class="guide-image rounded-10rpx" />
            <view class="guide-info flex-1 ml-20rpx">
              <text class="guide-title text-32rpx font-bold line-clamp-2">{{ guide.title }}</text>
              <text class="guide-desc text-26rpx color-grey line-clamp-2 mt-10rpx">{{ guide.description }}</text>
              <view class="flex justify-between items-center mt-15rpx">
                <view class="flex items-center">
                  <image :src="guide.authorAvatar" class="author-avatar" mode="aspectFill" />
                  <text class="author text-24rpx color-grey ml-10rpx">{{ guide.author }}</text>
                </view>
                <view class="flex items-center">
                  <text class="i-carbon-thumbs-up color-grey mr-5rpx"></text>
                  <text class="like-count text-24rpx color-grey">{{ guide.likeCount }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 成功案例 -->
      <view class="success-stories-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden">
        <view class="section-header px-30rpx py-20rpx flex justify-between items-center">
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">成功案例</text>
          </view>
          <view class="more-link flex items-center" @click="navigateTo('/pages/dating/success-stories')">
            <text class="text-26rpx color-primary">查看更多</text>
            <text class="i-carbon-chevron-right color-primary text-24rpx"></text>
          </view>
        </view>
        
        <view class="success-stories-list px-30rpx pb-30rpx">
          <view v-for="(story, index) in successStories" :key="index" 
            class="success-story-item p-20rpx mb-20rpx bg-white rounded-15rpx shadow-sm"
            @click="viewSuccessStory(story.id)">
            <view class="success-couple flex items-center mb-15rpx">
              <image :src="story.maleAvatar" class="couple-avatar" mode="aspectFill" />
              <text class="i-carbon-favorite-filled text-primary text-30rpx mx-15rpx"></text>
              <image :src="story.femaleAvatar" class="couple-avatar" mode="aspectFill" />
              <view class="flex-1 ml-20rpx">
                <text class="success-title text-30rpx font-bold">{{ story.title }}</text>
                <text class="success-subtitle text-24rpx color-grey">{{ story.subtitle }}</text>
              </view>
            </view>
            <text class="success-desc text-26rpx color-grey line-clamp-3 mb-15rpx">{{ story.description }}</text>
            <view class="flex justify-between items-center">
              <text class="text-24rpx color-grey">{{ story.date }}</text>
              <view class="views-count flex items-center">
                <text class="i-carbon-view color-grey mr-5rpx"></text>
                <text class="text-24rpx color-grey">{{ story.viewCount }}阅读</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部悬浮操作栏 -->
    <view class="bottom-action-bar flex justify-around items-center">
      <view v-for="(action, index) in bottomActions" :key="index" 
        class="action-item flex flex-col items-center" 
        @click="performAction(action.action)">
        <text :class="`action-icon i-carbon-${action.icon} ${activeAction === action.action ? 'text-primary' : 'color-grey'}`"></text>
        <text class="action-label text-22rpx" :class="activeAction === action.action ? 'text-primary' : 'color-grey'">{{ action.label }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 下拉刷新状态
const refreshing = ref(false);
const onRefresh = () => {
  // 模拟刷新数据
  setTimeout(() => {
    refreshing.value = false;
  }, 1500);
};

// Banner轮播图数据
const bannerList = ref([
  {
    id: 1,
    image: 'https://picsum.photos/700/300?random=1',
    title: '缘分良缘·高端相亲',
    subtitle: '百万精英等你来撩',
    link: '/pages/dating/activities/id=10001'
  },
  {
    id: 2,
    image: 'https://picsum.photos/700/300?random=2',
    title: '七夕脱单派对',
    subtitle: '一对一心动匹配',
    link: '/pages/dating/activities/id=10002'
  },
  {
    id: 3,
    image: 'https://picsum.photos/700/300?random=3',
    title: '恋爱学堂',
    subtitle: '专业老师教你脱单技巧',
    link: '/pages/dating/activities/id=10003'
  }
]);

// 功能导航
const featureNavigations = ref([
  {
    name: '速配约会',
    icon: 'lightning',
    color: 'red',
    path: '/pages/dating/quick-match'
  },
  {
    name: '线下活动',
    icon: 'calendar',
    color: 'blue',
    path: '/pages/dating/offline-events'
  },
  {
    name: '心理测试',
    icon: 'chart-evaluation',
    color: 'purple',
    path: '/pages/dating/psychological-test'
  },
  {
    name: '恋爱学堂',
    icon: 'education',
    color: 'green',
    path: '/pages/dating/love-academy'
  }
]);

// 精选活动
const datingActivities = ref([
  {
    id: '1001',
    image: 'https://picsum.photos/350/200?random=4',
    title: '周末相约·高质量单身交友派对',
    time: '本周六 14:00-17:00',
    location: '北京市朝阳区三里屯SOHO',
    type: '线下',
    price: '168',
    originalPrice: '298',
    currentCount: 32,
    maxCount: 50,
    isHot: true,
    isFull: false
  },
  {
    id: '1002',
    image: 'https://picsum.photos/350/200?random=5',
    title: '户外徒步·遇见心动的TA',
    time: '下周日 09:00-16:00',
    location: '北京市海淀区凤凰岭景区',
    type: '户外',
    price: '128',
    originalPrice: '198',
    currentCount: 28,
    maxCount: 40,
    isHot: false,
    isFull: false
  },
  {
    id: '1003',
    image: 'https://picsum.photos/350/200?random=6',
    title: '烘焙甜点·甜蜜邂逅',
    time: '本周日 13:30-16:30',
    location: '北京市西城区烘焙工作室',
    type: '室内',
    price: '198',
    originalPrice: '268',
    currentCount: 18,
    maxCount: 24,
    isHot: false,
    isFull: false
  },
  {
    id: '1004',
    image: 'https://picsum.photos/350/200?random=7',
    title: '高端酒会·精英约会',
    time: '下周五 19:30-22:00',
    location: '北京市朝阳区W酒店',
    type: '高端',
    price: '298',
    originalPrice: '498',
    currentCount: 40,
    maxCount: 40,
    isHot: false,
    isFull: true
  }
]);

// 用户筛选标签
const filterTabs = ref([
  { label: '全部', value: 'all' },
  { label: '同城', value: 'nearby' },
  { label: '已认证', value: 'verified' },
  { label: '新人', value: 'new' }
]);

const activeTab = ref('all');
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const currentCardIndex = ref(0);
const onSwiperChange = (e: any) => {
  currentCardIndex.value = e.detail.current;
};

// 推荐用户数据
const recommendUsers = ref([
  {
    id: '2001',
    avatar: 'https://picsum.photos/400/600?random=8',
    name: '小林',
    age: 28,
    gender: 'male',
    job: '产品经理',
    education: '硕士',
    tags: ['旅行', '摄影', '美食'],
    location: '朝阳区·2.5km',
    isVip: true,
    isVerified: true,
    isLiked: false,
    introduction: '热爱生活，喜欢尝试新鲜事物。工作认真负责，生活中是个有趣的人。希望找一个真诚、有共同话题的女生。',
  },
  {
    id: '2002',
    avatar: 'https://picsum.photos/400/600?random=9',
    name: '安妮',
    age: 26,
    gender: 'female',
    job: '财务',
    education: '本科',
    tags: ['电影', '音乐', '健身'],
    location: '海淀区·3.8km',
    isVip: false,
    isVerified: true,
    isLiked: true,
    introduction: '性格开朗，喜欢音乐和电影。平时喜欢健身和阅读，希望找一个有上进心、成熟稳重的男生共度余生。',
  },
  {
    id: '2003',
    avatar: 'https://picsum.photos/400/600?random=10',
    name: '小王',
    age: 29,
    gender: 'male',
    job: '设计师',
    education: '本科',
    tags: ['插画', '动漫', '宅家'],
    location: '西城区·5.2km',
    isVip: true,
    isVerified: false,
    isLiked: false,
    introduction: '性格内向但不社恐，喜欢宅在家里画画和看动漫。希望找一个能理解我的生活方式，同时也能一起成长的女生。',
  },
  {
    id: '2004',
    avatar: 'https://picsum.photos/400/600?random=11',
    name: '小美',
    age: 25,
    gender: 'female',
    job: '教师',
    education: '本科',
    tags: ['阅读', '瑜伽', '烘焙'],
    location: '东城区·4.1km',
    isVip: false,
    isVerified: true,
    isLiked: false,
    introduction: '教师，热爱教育事业。闲暇时喜欢做瑜伽和烘焙。希望找一个稳重、顾家、有责任感的男生组建家庭。',
  }
]);

// 根据筛选条件过滤用户
const filteredUsers = computed(() => {
  if (activeTab.value === 'all') {
    return recommendUsers.value;
  } else if (activeTab.value === 'nearby') {
    return recommendUsers.value.filter(user => parseFloat(user.location.split('·')[1].replace('km', '')) < 5);
  } else if (activeTab.value === 'verified') {
    return recommendUsers.value.filter(user => user.isVerified);
  } else if (activeTab.value === 'new') {
    // 假设前两个是新用户
    return recommendUsers.value.slice(0, 2);
  }
  return recommendUsers.value;
});

// 用户操作
const likeUser = (id: string) => {
  const user = recommendUsers.value.find(u => u.id === id);
  if (user) {
    user.isLiked = !user.isLiked;
    uni.showToast({
      title: user.isLiked ? '已喜欢' : '已取消喜欢',
      icon: 'none'
    });
  }
};

const dislikeUser = (id: string) => {
  uni.showToast({
    title: '已跳过',
    icon: 'none'
  });
  // 实际应用中可能需要将该用户从推荐列表中移除
};

const messageUser = (id: string) => {
  uni.navigateTo({
    url: `/pages/chat/conversation?userId=${id}`
  });
};

// 约会攻略数据
const datingGuides = ref([
  {
    id: '3001',
    image: 'https://picsum.photos/200/150?random=12',
    title: '第一次约会聊什么？教你避免尴尬冷场',
    description: '约会聊天话题技巧，让你轻松拉近两人距离，增进感情...',
    author: '情感顾问',
    authorAvatar: 'https://picsum.photos/50/50?random=21',
    likeCount: '2.5k',
  },
  {
    id: '3002',
    image: 'https://picsum.photos/200/150?random=13',
    title: '脱单秘籍：如何提升自己的魅力值',
    description: '从外表、谈吐到内在修养，全方位提升个人魅力，让你更容易获得异性青睐...',
    author: '形象顾问',
    authorAvatar: 'https://picsum.photos/50/50?random=22',
    likeCount: '3.2k',
  },
  {
    id: '3003',
    image: 'https://picsum.photos/200/150?random=14',
    title: '如何判断对方是否对你有好感？',
    description: '解读肢体语言和交流信号，让你准确把握对方的真实想法...',
    author: '心理专家',
    authorAvatar: 'https://picsum.photos/50/50?random=23',
    likeCount: '1.8k',
  }
]);

// 成功案例数据
const successStories = ref([
  {
    id: '4001',
    title: '缘分天注定',
    subtitle: '从相亲到相爱',
    maleAvatar: 'https://picsum.photos/60/60?random=15',
    femaleAvatar: 'https://picsum.photos/60/60?random=16',
    description: '我们是在一次线下相亲活动中认识的，一开始只是觉得聊得来，后来慢慢发现彼此越来越契合。经过半年的相处，我们决定走进婚姻的殿堂...',
    date: '2023-09-10',
    viewCount: '3.6k'
  },
  {
    id: '4002',
    title: '不期而遇的美好',
    subtitle: '速配牵手成功',
    maleAvatar: 'https://picsum.photos/60/60?random=17',
    femaleAvatar: 'https://picsum.photos/60/60?random=18',
    description: '原本对线上交友没什么期待，只是抱着试一试的心态。没想到系统匹配的第一个人就让我心动不已。两个月后我们见面了，感觉比线上更好，现在已经开始同居生活...',
    date: '2023-08-25',
    viewCount: '2.9k'
  }
]);

// 底部操作栏
const activeAction = ref('dating');
const bottomActions = ref([
  { label: '首页', icon: 'home', action: 'home' },
  { label: '消息', icon: 'chat', action: 'message' },
  { label: '缘分', icon: 'favorite', action: 'dating' },
  { label: '活动', icon: 'calendar', action: 'activities' },
  { label: '我的', icon: 'user-profile', action: 'profile' }
]);

// 导航函数
const navigateToFeature = (path: string) => {
  uni.navigateTo({ url: path });
};

const navigateTo = (url: string) => {
  uni.navigateTo({ url });
};

const viewActivityDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/activity-detail?id=${id}`
  });
};

const viewGuideDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/guide-detail?id=${id}`
  });
};

const viewSuccessStory = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/success-story?id=${id}`
  });
};

const performAction = (action: string) => {
  activeAction.value = action;
  switch (action) {
    case 'home':
      uni.switchTab({ url: '/pages/tabBar/home/<USER>' });
      break;
    case 'message':
      uni.switchTab({ url: '/pages/tabBar/message/index' });
      break;
    case 'dating':
      // 当前页面，不跳转
      break;
    case 'activities':
      uni.navigateTo({ url: '/pages/dating/activities' });
      break;
    case 'profile':
      uni.switchTab({ url: '/pages/tabBar/user/index' });
      break;
  }
};

// Banner点击
const onBannerClick = (item: any) => {
  uni.navigateTo({
    url: item.link
  });
};
</script>

<style lang="scss" scoped>
// 页面容器
.dating-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
}

// 顶部导航栏
.navbar-container {
  .navbar {
    height: 90rpx;
    background: white;
  }
  
  .navbar-actions {
    .action-icon {
      position: relative;
    }
    
    .notification-badge {
      position: absolute;
      top: 0;
      right: 0;
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #ff5778;
    }
  }
}

// 轮播图
.banner-section {
  .banner-swiper {
    height: 300rpx;
    position: relative;
  }
  
  .banner-image {
    width: 100%;
    height: 300rpx;
  }
  
  .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.5) 100%);
  }
  
  .banner-content {
    position: absolute;
    bottom: 30rpx;
    left: 30rpx;
    color: white;
    z-index: 1;
    
    .banner-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 6rpx;
      text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
    }
    
    .banner-subtitle {
      font-size: 24rpx;
      opacity: 0.9;
    }
  }
}

// 功能导航
.features-section {
  .feature-icon-bg {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .feature-name {
    margin-top: 10rpx;
  }
}

// 标题指示器
.title-indicator {
  width: 6rpx;
  height: 30rpx;
  background-color: #ff5778;
  margin-right: 15rpx;
  border-radius: 3rpx;
}

.color-primary {
  color: #ff5778;
}

// 隐藏滚动条
.hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
}

// 活动卡片
.activities-section {
  .activity-card {
    width: 300rpx;
    border-radius: 15rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
    background-color: white;
    
    .activity-image-container {
      position: relative;
    }
    
    .activity-image {
      width: 300rpx;
      height: 180rpx;
    }
    
    .activity-status {
      position: absolute;
      top: 15rpx;
      right: 15rpx;
      font-size: 20rpx;
      color: white;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      background-color: rgba(0, 0, 0, 0.5);
      
      &.status-hot {
        background-color: rgba(255, 87, 120, 0.8);
      }
      
      &.status-full {
        background-color: rgba(150, 150, 150, 0.8);
      }
    }
    
    .activity-type-tag {
      position: absolute;
      bottom: 15rpx;
      left: 15rpx;
      font-size: 20rpx;
      color: white;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
    }
    
    .activity-content {
      background-color: white;
    }
    
    .text-primary {
      color: #ff5778;
    }
    
    .people-count-tag {
      font-size: 20rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 6rpx 12rpx;
      border-radius: 4rpx;
    }
  }
}

// 用户推荐区域
.recommendation-section {
  .filter-tabs {
    .filter-tab {
      color: #666;
      font-size: 26rpx;
      transition: all 0.3s;
    }
    
    .active-tab {
      color: white;
      background-color: #ff5778;
    }
  }
  
  .user-cards-swiper {
    height: 800rpx;
    width: 100%;
  }
  
  .user-swiper-item {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
  }
  
  .user-card-big {
    width: 580rpx;
    border-radius: 20rpx;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
    transform: scale(0.9);
    transition: all 0.3s;
    
    &.card-active {
      transform: scale(1);
    }
    
    .user-card-image-container {
      position: relative;
      height: 500rpx;
    }
    
    .user-card-image {
      width: 100%;
      height: 100%;
    }
    
    .user-card-gradient {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 150rpx;
      background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
    }
    
    .vip-badge {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ffcc00;
    }
    
    .verified-badge {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      background-color: rgba(0, 0, 0, 0.3);
      color: white;
      font-size: 22rpx;
      padding: 6rpx 15rpx;
      border-radius: 25rpx;
      display: flex;
      align-items: center;
    }
    
    .user-card-actions {
      .action-btn {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 15rpx;
        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
        font-size: 40rpx;
      }
      
      .action-dislike {
        background-color: white;
        color: #ff5252;
      }
      
      .action-like {
        background-color: #ff5778;
        color: white;
      }
      
      .action-message {
        background-color: white;
        color: #4a90e2;
      }
    }
    
    .user-card-info {
      padding: 25rpx;
    }
    
    .gender-icon {
      width: 28rpx;
      height: 28rpx;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      color: #666;
    }
    
    .user-tag {
      font-size: 22rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 4rpx 15rpx;
      border-radius: 20rpx;
      margin-right: 15rpx;
      margin-bottom: 10rpx;
    }
  }
  
  .swiper-indicators {
    margin-top: 20rpx;
    
    .indicator-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #ddd;
      margin: 0 8rpx;
      transition: all 0.3s;
      
      &.active-dot {
        width: 24rpx;
        border-radius: 6rpx;
        background-color: #ff5778;
      }
    }
  }
}

// 约会攻略
.dating-guides-section {
  .guide-item {
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  .guide-image {
    width: 200rpx;
    height: 150rpx;
    flex-shrink: 0;
  }
  
  .author-avatar {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
  }
}

// 成功案例
.success-stories-section {
  .success-story-item {
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  .couple-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
  }
}

// 底部操作栏
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);
  
  .action-item {
    padding: 15rpx 0;
  }
  
  .action-icon {
    font-size: 44rpx;
    margin-bottom: 6rpx;
  }
  
  .text-primary {
    color: #ff5778;
  }
  
  .color-grey {
    color: #999;
  }
}

// 通用样式
.color-grey {
  color: #999;
}

.rounded-20rpx {
  border-radius: 20rpx;
}

.rounded-15rpx {
  border-radius: 15rpx;
}

.rounded-10rpx {
  border-radius: 10rpx;
}

.position-relative {
  position: relative;
}
</style>
