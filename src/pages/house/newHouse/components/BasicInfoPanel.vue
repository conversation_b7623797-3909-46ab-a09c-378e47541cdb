<template>
  <view class="basic-info-panel">
    <!-- 信息项列表 -->
    <view
      class="info-item"
      v-for="(item, index) in formattedInfoList"
      :key="index"
    >
      <view class="info-label text-999">{{ item.label }}</view>
      <view class="info-value">
        <text>{{ item.value || "暂无数据" }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 定义组件属性
interface Props {
  basicInfo: {
    alias?: string; // 楼盘别名
    features?: string; // 楼盘特色
    price?: string; // 参考均价
    totalPrice?: string; // 参考总价
    propertyType?: string; // 物业类型
    buildingType?: string; // 建筑类型
    greenBuilding?: string; // 绿色建筑
    decoration?: string; // 装修标准
    propertyYears?: number | string; // 产权年限
    brand?: string; // 品牌
    developer?: string; // 开发商
    area?: string; // 所在区域
    address?: string; // 楼盘地址
  };
}

const props = defineProps<Props>();

// 格式化信息列表，用于展示
const formattedInfoList = computed(() => {
  const { basicInfo } = props;

  return [
    { label: "楼盘别名", value: basicInfo.alias, type: "text" },
    { label: "楼盘特色", value: basicInfo.features || [], type: "tags" },
    { label: "参考均价", value: basicInfo.price, type: "text" },
    { label: "参考总价", value: basicInfo.totalPrice, type: "text" },
    { label: "物业类型", value: basicInfo.propertyType, type: "text" },
    { label: "建筑类型", value: basicInfo.buildingType, type: "text" },
    { label: "绿色建筑", value: basicInfo.greenBuilding, type: "text" },
    { label: "装修标准", value: basicInfo.decoration, type: "text" },
    {
      label: "产权年限",
      value: basicInfo.propertyYears ? `${basicInfo.propertyYears}年` : "",
      type: "text",
    },
    { label: "品牌", value: basicInfo.brand, type: "text" },
    { label: "开发商", value: basicInfo.developer, type: "text" },
    { label: "所在区域", value: basicInfo.area, type: "text" },
    { label: "楼盘地址", value: basicInfo.address, type: "address" },
  ].filter((item) => item.value !== undefined && item.value !== null);
});
</script>

<style lang="scss" scoped>
.basic-info-panel {
  .info-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin: 14rpx 0;

    .info-label {
      width: 160rpx;
      flex-shrink: 0;
      font-size: 28rpx;
      padding-right: 24rpx;
      line-height: 1.5;
    }

    .info-value {
      flex: 1;
      text-align: left;
      line-height: 1.5;
    }
  }
}
</style>
