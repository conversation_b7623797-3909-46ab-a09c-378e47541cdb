<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-section flex-x-between">
      <view class="search-bar flex-x" @tap="handleSearch">
        <text class="i-carbon-search text-gray-400 text-32rpx"></text>
        <text class="search-placeholder">搜索楼盘名称、位置</text>
      </view>
    </view>

    <!-- 筛选组件 -->
    <view class="filter-container">
      <FilterPanel @filter-change="handleFilterChange" />
    </view>

    <!-- 内容区域 - 使用z-paging组件 -->
    <z-paging
      ref="pagingRef"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :auto-show-back-to-top="true"
      :hide-empty-view="false"
      empty-view-text="暂无房源数据"
      :fixed="false"
    >
      <!-- 特色板块 - 热门楼盘推荐 -->
      <CustomCard
        v-if="pageNo === 1"
        margin="20rpx 32rpx 0 32rpx"
        padding="24rpx"
      >
        <view class="feature-section-content">
          <view class="section-header flex-x-between">
            <view class="section-title">
              <text class="title-text">热门楼盘</text>
              <text class="sub-title">近期热度最高的楼盘推荐</text>
            </view>
            <view class="more-btn" @tap="viewMoreHotHouse">
              <text>查看更多</text>
              <text class="i-carbon-arrow-right text-16px"></text>
            </view>
          </view>

          <!-- 横向滚动热门楼盘 -->
          <scroll-view
            scroll-x
            class="hot-houses-scroll"
            show-scrollbar="false"
          >
            <view class="hot-houses-container flex-x">
              <CustomCard
                v-for="(item, index) in hotHouses"
                :key="index"
                class="hot-house-item-card"
                margin="0 16rpx 0 0"
                padding="0"
                borderRadius="12rpx"
                :shadow="true"
                @tap="navigateToDetail(item.id)"
              >
                <view class="hot-house-item-content">
                  <image
                    :src="item.image"
                    mode="aspectFill"
                    class="hot-house-image"
                  ></image>
                  <view class="hot-house-info">
                    <text class="hot-house-name">{{ item.title }}</text>
                    <text class="hot-house-price">{{ item.price }}</text>
                  </view>
                </view>
              </CustomCard>
            </view>
          </scroll-view>
        </view>
      </CustomCard>

      <!-- 特色板块 - 开盘日历 -->
      <CustomCard
        v-if="pageNo === 1"
        margin="20rpx 32rpx 0 32rpx"
        padding="24rpx"
      >
        <view class="feature-section-content">
          <view class="section-header flex-x-between">
            <view class="section-title">
              <text class="title-text">开盘日历</text>
              <text class="sub-title">近期即将开盘楼盘信息</text>
            </view>
            <view class="more-btn" @tap="viewMoreCalendar">
              <text>全部</text>
              <text class="i-carbon-arrow-right text-16px"></text>
            </view>
          </view>

          <!-- 开盘日历卡片列表 -->
          <view class="calendar-cards-list">
            <CustomCard
              v-for="(item, index) in openingCalendar"
              :key="index"
              class="calendar-item-card"
              margin="0 0 16rpx 0"
              padding="20rpx"
              borderRadius="12rpx"
              :shadow="true"
              @tap="navigateToDetail(item.id)"
            >
              <view class="calendar-card-content flex-x">
                <view class="calendar-date flex-y-center">
                  <text class="calendar-month">{{ item.month }}</text>
                  <text class="calendar-day">{{ item.day }}</text>
                </view>
                <view class="calendar-info flex-y">
                  <text class="calendar-house-name">{{ item.title }}</text>
                  <text class="calendar-house-location">{{
                    item.location
                  }}</text>
                  <view class="calendar-house-price">{{ item.price }}</view>
                </view>
                <view class="appointment-btn" @tap.stop="appointHouse(item.id)">
                  <text>预约看房</text>
                </view>
              </view>
            </CustomCard>
          </view>
        </view>
      </CustomCard>

      <!-- 新房楼盘列表标题 -->
      <view
        class="list-header-wrapper"
        v-if="pageNo === 1 && houseList.length > 0"
      >
        <CustomCard margin="20rpx 32rpx 0 32rpx" padding="24rpx 24rpx 0 24rpx">
          <text class="list-title">全部楼盘</text>
        </CustomCard>
      </view>

      <!-- 楼盘列表 -->
      <view class="house-list-container">
        <CustomCard
          v-for="house in houseList"
          :key="house.id"
          class="house-item-card"
          margin="20rpx 32rpx"
          padding="0"
          @tap="navigateToDetail(house.id)"
        >
          <view class="house-item-content">
            <!-- 房源图片 -->
            <view class="house-image-container">
              <image
                :src="house.image"
                mode="aspectFill"
                class="house-image"
                :lazy-load="true"
              />
              <view
                v-if="house.status"
                class="status-tag-on-image"
                :class="getStatusClass(house.status)"
              >
                {{ house.status }}
              </view>
              <view v-if="house.specialOffer" class="special-tag-on-image">
                <text>{{ house.specialOffer }}</text>
              </view>
            </view>

            <!-- 房源信息 -->
            <view class="house-info-wrapper">
              <view class="house-title-row flex-x-between">
                <text class="title-text">{{ house.title }}</text>
                <view class="house-type-tag">{{
                  house.propertyType || "住宅"
                }}</view>
              </view>
              <view class="location-info">
                <text class="location-text">{{ house.location }}</text>
              </view>
              <view class="price-section flex-x-between">
                <text class="price-value">{{ house.price }}</text>
                <text class="price-desc" v-if="house.priceRange"
                  >总价{{ house.priceRange }}</text
                >
              </view>
              <view v-if="house.tags && house.tags.length" class="tags-section">
                <text v-for="tag in house.tags" :key="tag" class="tag">
                  {{ tag }}
                </text>
              </view>
              <view v-if="house.attentionCount" class="attention-info">
                <text class="attention-text"
                  >近期有{{ house.attentionCount }}人关注</text
                >
              </view>
            </view>
          </view>
        </CustomCard>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import type { NewHouseItem } from "@/types/house";
import FilterPanel from "./components/FilterPanel.vue";
import CustomCard from "@/components/common/Card.vue";

// z-paging引用
const pagingRef = ref(null);

// 当前页码
const pageNo = ref(1);

// 响应式数据
const currentLocation = ref("北京");
const showFilterModal = ref(false);

// 筛选条件
const currentFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  developer: "",
  decoration: "",
});

// 选中的筛选条件
const selectedFilters = reactive({
  price: "",
  houseType: "",
  features: [] as string[],
});

// 保存原始房源列表数据
const originalHouseList = [
  {
    id: "1",
    image: "https://picsum.photos/seed/house-1/300/200",
    title: "元垄",
    location: "石景山·石景山其它/建面120-196㎡/3,4居",
    price: "75000元/平",
    originalPrice: "78000元/平",
    specialOffer: "有15套特价房",
    buildTypes: ["3居", "4居"],
    tags: ["近地铁", "综合商场", "公园"],
    openTime: "",
    attentionCount: 29,
    hasVR: false,
    status: "在售",
    priceRange: "950-1560万",
    propertyType: "住宅",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/house-2/300/200",
    title: "招商云璟境",
    location: "通州·梨园/建面79-128㎡/3,4居",
    price: "60000元/平",
    originalPrice: "63000元/平",
    specialOffer: "有9套特价房",
    buildTypes: ["2居", "3居"],
    tags: ["近地铁", "医疗配套", "期房"],
    openTime: "",
    attentionCount: 33,
    hasVR: true,
    status: "在售",
    priceRange: "470-830万",
    propertyType: "住宅",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/house-3/300/200",
    title: "京投发展森与天成",
    location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
    price: "78000元/平",
    originalPrice: "80000元/平",
    specialOffer: "",
    buildTypes: ["1居", "2居", "3居", "4居"],
    tags: ["近地铁", "公园", "期房"],
    openTime: "",
    attentionCount: 0,
    hasVR: true,
    status: "在售",
    priceRange: "430-1150万",
    propertyType: "住宅",
  },
  {
    id: "4",
    image: "https://picsum.photos/seed/house-4/300/200",
    title: "清樾府",
    location: "昌平·沙河/建面86-143㎡/3,4居",
    price: "46000元/平",
    originalPrice: "48000元/平",
    specialOffer: "",
    buildTypes: ["2居", "3居", "4居"],
    tags: ["期房", "小三居", "低密居所"],
    openTime: "",
    attentionCount: 39,
    hasVR: false,
    status: "在售",
    priceRange: "410-680万",
    propertyType: "住宅",
  },
  {
    id: "5",
    image: "https://picsum.photos/seed/house-5/300/200",
    title: "地铁10号线350米 高楼层 精装修",
    location: "2室1厅 | 81.93㎡ | 西南",
    price: "349万",
    originalPrice: "42,598元/平",
    specialOffer: "",
    buildTypes: ["2居"],
    tags: ["满五", "小高层", "户型方正"],
    openTime: "",
    attentionCount: 0,
    hasVR: false,
    status: "二手房",
    propertyType: "二手房",
  },
];

// 热门楼盘数据
const hotHouses = ref([
  {
    id: "h1",
    image: "https://picsum.photos/seed/hot-1/300/200",
    title: "首开龙湖·云著",
    price: "62000元/平",
    tags: ["品牌开发商", "低密度", "热销楼盘"],
  },
  {
    id: "h2",
    image: "https://picsum.photos/seed/hot-2/300/200",
    title: "华润西山金茂府",
    price: "85000元/平",
    tags: ["品牌开发商", "地铁房", "公园"],
  },
  {
    id: "h3",
    image: "https://picsum.photos/seed/hot-3/300/200",
    title: "万科蓝山",
    price: "58000元/平",
    tags: ["品牌开发商", "低总价", "精装修"],
  },
  {
    id: "h4",
    image: "https://picsum.photos/seed/hot-4/300/200",
    title: "朗润园",
    price: "53000元/平",
    tags: ["低总价", "地铁房", "配套齐全"],
  },
]);

// 开盘日历数据
const openingCalendar = ref([
  {
    id: "c1",
    month: "5月",
    day: "15",
    title: "首创·天阅西山",
    location: "门头沟·永定",
    price: "约68000元/平",
  },
  {
    id: "c2",
    month: "5月",
    day: "20",
    title: "北京经开·自然家园",
    location: "大兴·亦庄",
    price: "约55000元/平",
  },
  {
    id: "c3",
    month: "6月",
    day: "1",
    title: "融创·壹号院",
    location: "海淀·上地",
    price: "约75000元/平",
  },
]);

// 房源列表数据
const houseList = ref<NewHouseItem[]>([]);

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

const selectLocation = () => {
  uni.showActionSheet({
    itemList: ["北京", "上海", "广州", "深圳", "杭州"],
    success: (res) => {
      const locations = ["北京", "上海", "广州", "深圳", "杭州"];
      currentLocation.value = locations[res.tapIndex];
      // 切换城市后重新加载数据
      if (pagingRef.value) {
        pagingRef.value.reload();
      }
    },
  });
};

// 查看更多热门楼盘
const viewMoreHotHouse = () => {
  uni.showToast({
    title: "查看更多热门楼盘",
    icon: "none",
  });
};

// 查看更多开盘日历
const viewMoreCalendar = () => {
  uni.showToast({
    title: "查看更多开盘日历",
    icon: "none",
  });
};

// 预约看房
const appointHouse = (id: string) => {
  uni.showToast({
    title: "预约看房功能开发中",
    icon: "none",
  });
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "在售":
      return "status-selling";
    case "热销":
      return "status-hot";
    case "即将开盘":
      return "status-coming";
    case "售罄":
      return "status-sold";
    case "二手房":
      return "status-second";
    default:
      return "status-default";
  }
};

const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

// z-paging查询数据方法
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 设置当前页码，用于条件判断是否展示特色板块
  pageNo = pageNo;

  // 这里模拟分页数据请求
  // 实际开发中应该调用API获取数据
  return new Promise((resolve) => {
    setTimeout(() => {
      if (pageNo === 1) {
        // 第一页返回初始数据
        resolve({
          list: originalHouseList,
          total: 10, // 总数据量
        });
      } else {
        // 模拟加载第二页数据
        const moreData = [
          {
            id: `${Date.now()}`,
            title: "保利和光尘樾",
            image: "https://picsum.photos/seed/house-6/300/200",
            location: "海淀区 · 西二旗",
            price: "68000元/平",
            priceRange: "550-720万",
            status: "在售",
            buildTypes: ["高层"],
            tags: ["地铁房", "科技园区"],
            hasVR: false,
            openTime: "现房销售",
            developer: "保利地产",
            propertyType: "住宅",
            propertyYears: 70,
          },
        ];
        resolve({
          list: pageNo <= 2 ? moreData : [], // 第三页后无数据
          total: 10,
        });
      }
    }, 1000);
  });
};

// 处理筛选变化
const handleFilterChange = (filters: any) => {
  Object.assign(currentFilters, filters);
  console.log("筛选条件已更新:", currentFilters);

  // 重新加载数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

onMounted(() => {
  // 页面加载时的初始化操作
  console.log("页面已加载");
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;

  /* 顶部导航栏样式 */
  .nav-header {
    padding: 20rpx 32rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }

    .page-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .location-selector {
      padding: 8rpx 16rpx;
      background-color: #f5f5f5;
      border-radius: 24rpx;
      font-size: 28rpx;
      color: #333;
    }
  }

  /* 搜索栏样式 */
  .search-section {
    padding: 24rpx 32rpx;
    background: linear-gradient(to right, #fafafa, #f0f0f0);
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  }

  .search-bar {
    height: 72rpx;
    padding: 0 24rpx;
    background-color: #fff;
    border-radius: 36rpx;
    gap: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }

  .search-placeholder {
    color: #999;
    font-size: 28rpx;
  }

  /* 筛选栏样式 */
  .filter-container {
    position: sticky;
    top: 0;
    z-index: 100;
    background: linear-gradient(to bottom, #ffffff, #f9f9f9);
    border-bottom: 1rpx solid #f0f0f0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  }

  /* 特色板块样式 */
  .feature-section {
    padding: 24rpx 32rpx;
    margin-bottom: 16rpx;
    background-color: #fff;
  }

  .feature-section-content {
    // background-color: #fff;
    // padding: 24rpx 32rpx;
    // margin-bottom: 16rpx;
  }

  .section-header {
    margin-bottom: 20rpx;
  }

  .section-title {
    display: flex;
    flex-direction: column;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .sub-title {
      font-size: 24rpx;
      color: #999;
    }
  }

  .more-btn {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #3b7fff;
  }

  /* 热门楼盘横向滚动 */
  .hot-houses-scroll {
    width: 100%;
  }

  .hot-houses-container {
    padding: 10rpx 0;
    gap: 0;
  }

  .hot-house-item-card {
    display: inline-block;
    width: 280rpx;
    &:last-child {
      margin-right: 0 !important;
    }
  }

  .hot-house-item-content {
    width: 100%;
    // border-radius: $border-radius-base;
    // overflow: hidden;
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .hot-house-image {
      width: 100%;
      height: 180rpx;
      // border-radius: $border-radius-base $border-radius-base 0 0;
    }

    .hot-house-info {
      padding: 16rpx;
      background-color: #fff;

      .hot-house-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
      }

      .hot-house-price {
        font-size: 26rpx;
        color: #ff5a5f;
        font-weight: 500;
      }
    }
  }

  /* 开盘日历样式 */
  .calendar-cards-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  .calendar-item-card {
    // margin, padding, borderRadius, shadow 由CustomCard props控制
  }

  .calendar-card-content {
    // padding: 20rpx; // 已由Card的padding控制
    // background-color: #fff; // 由Card控制
    // border-radius: $border-radius-base; // 由Card控制
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); // 由Card控制
    align-items: center; // 确保内容垂直居中

    .calendar-date {
      width: 90rpx; // 调整尺寸
      height: 90rpx;
      background-color: #3b7fff;
      border-radius: 12rpx; // 调整圆角
      color: #fff;
      margin-right: 24rpx; // 调整间距
      flex-shrink: 0;

      .calendar-month {
        font-size: 26rpx; // 调整字号
        margin-bottom: 4rpx;
      }

      .calendar-day {
        font-size: 36rpx; // 调整字号
        font-weight: 600;
      }
    }

    .calendar-info {
      flex: 1;
      min-width: 0; // 允许flex item收缩

      .calendar-house-name {
        font-size: 30rpx; // 调整字号
        font-weight: 500;
        color: #333;
        margin-bottom: 10rpx; // 调整间距
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .calendar-house-location {
        font-size: 26rpx; // 调整字号
        color: #666;
        margin-bottom: 10rpx; // 调整间距
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .calendar-house-price {
        font-size: 26rpx; // 调整字号
        color: #ff5a5f;
        font-weight: 500; // 加粗
      }
    }

    .appointment-btn {
      padding: 12rpx 28rpx; // 调整内边距
      background-color: #3b7fff;
      color: #fff;
      font-size: 26rpx; // 调整字号
      border-radius: 30rpx; // 调整圆角
      margin-left: 16rpx; // 与左侧信息间距
      white-space: nowrap; // 防止文字换行
    }
  }

  /* 列表标题 */
  .list-header-wrapper {
    padding: 24rpx 32rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .list-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 20rpx;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 28rpx; // 调整高度
      background-color: #3b7fff;
      border-radius: 3rpx;
    }
  }

  /* 房源列表样式 */
  z-paging {
    flex: 1;
    background-color: #f8f9fa;
  }

  .house-item-card {
    background-color: #fff;
    border-radius: $border-radius-lg;
    margin: 24rpx 32rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;

    &:active {
      transform: translateY(2rpx) scale(0.99);
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
    }
  }

  .house-item-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .house-image-container {
    position: relative;
    height: 380rpx; // 增加图片高度
    overflow: hidden; // 确保图片圆角（如果Card没padding）
    border-top-left-radius: inherit; // 继承Card的圆角
    border-top-right-radius: inherit;
  }

  .house-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .house-item-card:hover .house-image {
    // PC端悬浮效果
    transform: scale(1.05);
  }

  .status-tag-on-image {
    // 修改类名以区分
    position: absolute;
    top: 20rpx; // 调整位置
    left: 20rpx;
    padding: 8rpx 18rpx; // 调整内边距
    border-radius: 8rpx; // 调整圆角
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  }

  .status-selling {
    background-color: #3b7fff;
  }

  .status-hot {
    background-color: #ff5a5f;
  }

  .status-coming {
    background-color: #52c41a;
  }

  .status-sold {
    background-color: #999;
  }

  .status-second {
    background-color: #3b7fff;
  }

  .special-tag-on-image {
    // 修改类名以区分
    position: absolute;
    bottom: 20rpx; // 调整位置到底部
    left: 20rpx;
    padding: 8rpx 18rpx;
    background-color: rgba(255, 90, 95, 0.85); // 轻微调整透明度
    border-radius: 8rpx;
    color: #fff;
    font-size: 22rpx;
    font-weight: 500;
  }

  .house-info-wrapper {
    // 新增，用于控制信息区域的padding
    padding: 24rpx;
  }

  .house-title-row {
    margin-bottom: 12rpx; // 调整间距
    align-items: flex-start; // 标题可能多行
  }

  .title-text {
    // 复用，但确保在Card内样式正确
    color: #222; // 加深颜色
    font-size: 32rpx;
    font-weight: 600;
    line-height: 1.4;
    flex: 1;
    margin-right: 16rpx; // 与类型标签间距
  }

  .house-type-tag {
    // 复用，但确保在Card内样式正确
    padding: 4rpx 12rpx;
    background-color: #eff2f5; // 淡雅背景色
    border-radius: 6rpx; // 调整圆角
    color: #555; // 调整颜色
    font-size: 22rpx;
    white-space: nowrap;
  }

  .location-info {
    margin-bottom: 12rpx; // 调整间距
  }

  .location-text {
    // 复用
    color: #666;
    font-size: 26rpx;
    line-height: 1.4;
  }

  .price-section {
    margin-bottom: 16rpx;
    align-items: baseline; // 价格和描述基线对齐
  }

  .price-value {
    // 复用
    color: #ff5a5f;
    font-size: 34rpx; // 稍大
    font-weight: 600;
  }

  .price-desc {
    // 复用
    font-size: 24rpx;
    color: #999;
    margin-left: 8rpx; // 与主价格间距
  }

  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 16rpx;
  }

  .tag {
    // 复用
    padding: 6rpx 16rpx; // 调整内边距
    background-color: #f0f2f5; // 更淡的背景
    border-radius: 6rpx;
    color: #555; // 调整颜色
    font-size: 22rpx;
  }

  .attention-info {
    margin-top: 20rpx; // 调整间距
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0; // 更清晰的分割线
  }

  .attention-text {
    // 复用
    color: #888; // 调整颜色
    font-size: 24rpx;
  }
}
</style>
