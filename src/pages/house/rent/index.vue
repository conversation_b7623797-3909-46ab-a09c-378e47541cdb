<template>
  <view class="container">
    <!-- 顶部搜索栏 -->
    <view class="search-header">
      <view class="search-box" @tap="goToSearch">
        <text class="i-carbon-search text-28rpx color-grey mr-12rpx"></text>
        <text class="search-placeholder">搜索小区、地标、学校</text>
      </view>
    </view>

    <!-- 筛选面板组件 -->
    <HouseFilterPanel
      ref="filterPanelRef"
      v-model:sticky="isFilterSticky"
      :initial-filters="filterParams"
      @filter-change="onFilterChange"
      @filter-reset="onFilterReset"
      @menuOpened="handleFilterMenuOpened"
    />

    <!-- 使用z-paging组件代替scroll-view -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-scrollbar="false"
      @scrolltolower="loadMore"
      :refresher-threshold="80"
      @refresherrefresh="onRefresh"
      :style="{ paddingTop: isFilterSticky ? '88rpx' : '0' }"
    >
      <!-- 功能区导航栏 -->
      <scroll-view class="function-nav" scroll-x="true" show-scrollbar="false">
        <view class="nav-list">
          <view
            v-for="(item, index) in functionNavs"
            :key="index"
            class="nav-item"
            :class="{ active: activeFunctionIndex === index }"
            @tap="switchFunction(index)"
          >
            <view
              class="nav-icon-container"
              :class="{ active: activeFunctionIndex === index }"
              :style="{
                backgroundColor:
                  activeFunctionIndex === index
                    ? '#FF6D00'
                    : 'rgba(0,0,0,0.05)',
                borderColor:
                  activeFunctionIndex === index ? '#FF6D00' : item.color,
              }"
            >
              <text
                :class="[item.icon, 'nav-icon']"
                :style="{
                  color: activeFunctionIndex === index ? '#ffffff' : item.color,
                }"
              ></text>
            </view>
            <text
              class="nav-text"
              :style="{
                color: activeFunctionIndex === index ? '#FF6D00' : '#666',
              }"
              >{{ item.name }}</text
            >
            <view
              v-if="activeFunctionIndex === index"
              class="nav-indicator"
            ></view>
          </view>
        </view>
      </scroll-view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in houseList"
          :key="house.id"
          class="house-card"
          @tap="goToDetail(house.id)"
        >
          <!-- 房源图片 -->
          <view class="image-container">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image"
              :lazy-load="true"
            />
            <!-- VR看房标签 -->
            <view v-if="house.hasVR" class="vr-tag">
              <text class="i-carbon-view text-20rpx mr-6rpx"></text>
              <text class="vr-text">VR看房</text>
            </view>
            <!-- 新上标签 -->
            <view v-if="house.isNew" class="new-tag">新上</view>
          </view>

          <!-- 房源信息 -->
          <view class="house-info">
            <!-- 上半部分：标题和基本信息 -->
            <view class="info-top">
              <!-- 标题行 -->
              <text class="house-title">{{ formatTitle(house) }}</text>

              <!-- 房源详情 -->
              <text class="details">{{ formatDetails(house) }}</text>

              <!-- 标签区域 -->
              <view class="tags-container">
                <text
                  v-for="(tag, tagIndex) in house.tags.slice(0, 4)"
                  :key="tagIndex"
                  class="tag"
                  >{{ tag }}</text
                >
              </view>
            </view>

            <!-- 下半部分：价格信息 -->
            <view class="info-bottom">
              <view class="price-row">
                <text class="price">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 无数据时的提示 -->
        <view v-if="houseList.length === 0" class="empty-state">
          <view
            class="empty-icon i-carbon-no-image text-120rpx color-grey-light"
          ></view>
          <text class="empty-text">暂无符合条件的房源</text>
          <text class="empty-subtext">您可以尝试调整筛选条件</text>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import HouseFilterPanel from "@/components/house/HouseFilterPanel.vue";

// 房源数据接口定义
interface RentHouse {
  id: string;
  title: string;
  community: string;
  layout: string; // 如"2室1厅1卫"
  area: string;
  floor: string;
  direction: string;
  price: string;
  location: string;
  tags: string[];
  image: string;
  hasVR?: boolean;
  isNew?: boolean;
  paymentMethod?: string; // 如"押一付一"
  utilities?: string; // 如"民水民电"
  rentType?: string; // 整租/合租
}

// 筛选面板引用
const filterPanelRef = ref(null);

// 分页组件引用
const paging = ref(null);

// 筛选面板是否吸顶
const isFilterSticky = ref(false);

// 筛选参数
const filterParams = reactive({
  area: "",
  price: "",
  layout: "",
  rentType: "",
  more: [],
});

// 功能导航当前选中索引
const activeFunctionIndex = ref(0);

// 功能导航数据 - 使用更美观的图标
const functionNavs = reactive([
  {
    name: "公寓",
    icon: "i-carbon-building-insights-3",
    type: "apartment",
    color: "#FF6B35",
  },
  {
    name: "合租",
    icon: "i-carbon-user-multiple",
    type: "shared",
    color: "#4ECDC4",
  },
  { name: "整租", icon: "i-carbon-home", type: "whole", color: "#45B7D1" },
  {
    name: "品牌公寓",
    icon: "i-carbon-star-filled",
    type: "brand",
    color: "#F7DC6F",
  },
  {
    name: "长租公寓",
    icon: "i-carbon-time-filled",
    type: "longterm",
    color: "#BB8FCE",
  },
  {
    name: "民宿",
    icon: "i-carbon-location-heart",
    type: "bnb",
    color: "#F1948A",
  },
]);

// 房源列表数据
const houseList = ref<RentHouse[]>([]);

// 页面滚动位置
const scrollTop = ref(0);

// 页面方法
const goBack = () => {
  uni.navigateBack();
};

const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

const goToMap = () => {
  uni.navigateTo({
    url: "/pages/house/rent/map",
  });
};

const goToDetail = (houseId: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${houseId}`,
  });
};

// 功能导航切换
const switchFunction = (index: number) => {
  activeFunctionIndex.value = index;
  // 使用z-paging的reload方法重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 筛选条件变化
const onFilterChange = (filters) => {
  Object.assign(filterParams, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 重置筛选条件
const onFilterReset = () => {
  Object.keys(filterParams).forEach((key) => {
    if (key === "more") {
      filterParams[key] = [];
    } else {
      filterParams[key] = "";
    }
  });

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// z-paging查询房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 模拟网络请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData: RentHouse[] = [
      {
        id: "1",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "2",
        title: "整租1居 · 金域华府",
        community: "金域华府",
        layout: "1室1厅1卫",
        area: "55",
        floor: "中楼层",
        direction: "南",
        price: "3800",
        location: "望京",
        tags: ["近地铁", "精装修", "电梯房"],
        image: "https://picsum.photos/seed/rent2/240/200",
        hasVR: false,
        isNew: true,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "3",
        title: "合租3居 · 远洋天地",
        community: "远洋天地",
        layout: "3室2厅2卫",
        area: "20",
        floor: "低楼层",
        direction: "东",
        price: "2600",
        location: "酒仙桥",
        tags: ["独立卫生间", "拎包入住", "押一付一"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: true,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "合租",
      },
      {
        id: "4",
        title: "整租2居 · 富力城",
        community: "富力城",
        layout: "2室1厅1卫",
        area: "85",
        floor: "高楼层",
        direction: "南北",
        price: "6800",
        location: "双井",
        tags: ["南北通透", "精装修", "近地铁"],
        image: "https://picsum.photos/seed/rent4/240/200",
        hasVR: false,
        isNew: false,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "5",
        title: "整租3居 · 世茂维拉",
        community: "世茂维拉",
        layout: "3室2厅2卫",
        area: "120",
        floor: "中楼层",
        direction: "南北",
        price: "9500",
        location: "亦庄",
        tags: ["南北通透", "精装修", "电梯房"],
        image: "https://picsum.photos/seed/rent5/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
    ];

    // 根据筛选条件过滤数据
    let filteredData = [...mockData];

    // 根据功能导航过滤
    const functionType = functionNavs[activeFunctionIndex.value].type;
    if (functionType === "shared") {
      filteredData = filteredData.filter((house) => house.rentType === "合租");
    } else if (functionType === "whole") {
      filteredData = filteredData.filter((house) => house.rentType === "整租");
    }

    // 根据筛选参数过滤
    if (filterParams.area) {
      filteredData = filteredData.filter((house) =>
        house.location.includes(filterParams.area)
      );
    }

    if (filterParams.price) {
      const [min, max] = filterParams.price.split("-").map(Number);
      if (min && max) {
        filteredData = filteredData.filter((house) => {
          const price = Number(house.price);
          return price >= min && price <= max;
        });
      } else if (min) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) >= min
        );
      } else if (max) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) <= max
        );
      }
    }

    if (filterParams.layout) {
      filteredData = filteredData.filter((house) =>
        house.layout.startsWith(`${filterParams.layout}室`)
      );
    }

    if (filterParams.rentType) {
      filteredData = filteredData.filter(
        (house) => house.rentType === filterParams.rentType
      );
    }

    if (filterParams.more && filterParams.more.length > 0) {
      filteredData = filteredData.filter((house) => {
        return filterParams.more.some((tag) => {
          if (tag === "south-north") return house.direction === "南北";
          if (tag === "south") return house.direction === "南";
          if (tag === "east") return house.direction === "东";
          if (tag === "west") return house.direction === "西";
          if (tag === "north") return house.direction === "北";
          if (tag === "low") return house.floor.includes("低");
          if (tag === "middle") return house.floor.includes("中");
          if (tag === "high") return house.floor.includes("高");
          return house.tags.some((t) => t.includes(tag));
        });
      });
    }

    // 返回分页数据
    paging.value.complete(filteredData);
  }, 1000);
};

// 下拉刷新
const onRefresh = () => {
  if (paging.value) {
    paging.value.reload();
  }
};

// 加载更多 - 这个方法在z-paging中已自动处理
const loadMore = () => {
  // z-paging会自动处理加载更多逻辑
  console.log("加载更多");
};

// 格式化标题
const formatTitle = (house: RentHouse) => {
  const rentType = house.rentType || "整租";
  const rooms = house.layout.match(/(\d+)室/)
    ? house.layout.match(/(\d+)室/)![1]
    : "";
  return `${rentType}${rooms}居 · ${house.community}`;
};

// 格式化详情
const formatDetails = (house: RentHouse) => {
  return `${house.layout} | ${house.area}㎡ | ${house.direction} | ${house.floor}`;
};

// 页面滚动事件
const onPageScroll = (e) => {
  // 保存当前滚动位置
  scrollTop.value = e.scrollTop;

  // 当滚动位置超过一定值时，设置筛选面板为吸顶状态
  if (e.scrollTop > 100 && !isFilterSticky.value) {
    isFilterSticky.value = true;
  } else if (
    e.scrollTop <= 100 &&
    isFilterSticky.value &&
    filterPanelRef.value &&
    filterPanelRef.value.activeMenuIndex === -1
  ) {
    // 当滚动位置小于等于阈值，且筛选面板没有打开菜单时，取消吸顶状态
    isFilterSticky.value = false;
  }
};

// 处理筛选菜单打开事件
const handleFilterMenuOpened = () => {
  if (filterPanelRef.value) {
    const query = uni.createSelectorQuery();
    // @ts-ignore
    query
      .select(".filter-menu")
      .boundingClientRect((data) => {
        // @ts-ignore
        const rect = Array.isArray(data) ? data[0] : data;
        if (rect && rect.top !== 0) {
          uni.pageScrollTo({
            scrollTop: scrollTop.value + rect.top,
            duration: 300,
          });
        }
      })
      .exec();
  }
};

// 页面加载时初始化数据
onMounted(() => {
  // z-paging会自动在挂载后调用queryHouseList
});

// 注册onPageScroll生命周期钩子
defineExpose({
  onPageScroll,
});
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部搜索栏 */
.search-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  height: 64rpx;
  background-color: #f8f9fa;
  border-radius: 32rpx;
  padding: 0 24rpx;
  margin: 0 16rpx;
  transition: all 0.3s ease;

  &:active {
    background-color: #e9ecef;
  }
}

.search-placeholder {
  font-size: 28rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 功能导航栏 */
.function-nav {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 16rpx;
}

.nav-list {
  display: flex;
  padding: 0 24rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 32rpx;
  position: relative;
  transition: all 0.3s ease;

  &.active {
    .nav-text {
      font-weight: 600;
    }
  }
}

.nav-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.nav-icon {
  font-size: 40rpx;
}

.nav-text {
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff6d00;
  border-radius: 2rpx;
}

/* 房源列表 */
.house-list {
  padding: 24rpx;
}

/* 房源卡片 */
.house-card {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
}

.image-container {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  flex-shrink: 0;
}

.house-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
}

.vr-tag {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.vr-text {
  font-size: 22rpx;
}

.new-tag {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #ff5a5f;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-top-left-radius: 12rpx;
  border-bottom-right-radius: 8rpx;
}

/* 房源信息 */
.house-info {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.info-top {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-bottom {
  margin-top: 8rpx;
}

.house-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.price-row {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6d00;
}

.price-unit {
  font-size: 26rpx;
  color: #ff6d00;
  margin-left: 6rpx;
}

.details {
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  padding: 6rpx 12rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: #ff6d00;
  font-size: 22rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

/* 无数据时的提示样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 48rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtext {
  font-size: 26rpx;
  color: #999;
}

/* 通用样式类 */
.color-main {
  color: #333;
}

.color-grey {
  color: #999;
}

.color-grey-light {
  color: #ccc;
}

.color-primary {
  color: #ff6d00;
}

.text-20rpx {
  font-size: 20rpx;
}

.text-28rpx {
  font-size: 28rpx;
}

.text-32rpx {
  font-size: 32rpx;
}

.text-36rpx {
  font-size: 36rpx;
}

.text-120rpx {
  font-size: 120rpx;
}

.mr-6rpx {
  margin-right: 6rpx;
}

.mr-8rpx {
  margin-right: 8rpx;
}

.mr-12rpx {
  margin-right: 12rpx;
}

.mr-16rpx {
  margin-right: 16rpx;
}

.ml-8rpx {
  margin-left: 8rpx;
}

.mb-24rpx {
  margin-bottom: 24rpx;
}

/* 添加筛选弹出层样式 */
.custom-popup-content {
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  max-height: 70vh;
}

.filter-list {
  padding: 24rpx 0;
}

.filter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  position: relative;
  transition: all 0.3s ease;

  &.active {
    background-color: #fff8f2;

    .filter-text {
      color: #ff6d00;
      font-weight: 600;
    }

    .filter-icon {
      color: #ff6d00;
    }
  }

  &:active {
    background-color: #f9f9f9;
  }
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.filter-icon {
  font-size: 32rpx;
}

/* 更多筛选样式 */
.more-filter {
  padding: 24rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.group-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.group-item {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 32rpx;
  transition: all 0.3s;

  &.active {
    background-color: #fff0e6;
    border-color: #ff6d00;

    .group-item-text {
      color: #ff6d00;
      font-weight: 500;
    }
  }
}

.group-item-text {
  font-size: 26rpx;
  color: #666;
}

.filter-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
  margin-top: 24rpx;
  padding-top: 24rpx;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16rpx;
}

.confirm-btn {
  background-color: #ff6d00;
  color: #fff;
}
</style>
