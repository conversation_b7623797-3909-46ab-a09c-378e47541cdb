---
description: 
globs: 
alwaysApply: false
---
# 组件使用指南

本项目使用多种组件库和自定义组件，包括uni-ui、ThorUI和自定义组件。

## 组件目录结构

- `components/common`: 通用组件
- `components/home`: 首页相关组件
- `components/house`: 房产相关组件
- `components/job`: 招聘相关组件
- `components/thorui`: ThorUI组件库

## 常用组件

### 帖子项组件

[PostItem.vue](mdc:src/components/PostItem.vue) - 用于显示社区帖子条目

### 图片加载组件

[ImageLoader.vue](mdc:src/components/ImageLoader.vue) - 处理图片加载和错误情况

## 第三方组件库

### uni-ui组件

项目使用`@dcloudio/uni-ui`组件库，可以直接使用，如：

```vue
<template>
  <uni-forms>
    <uni-forms-item label="用户名">
      <uni-easyinput v-model="form.username" placeholder="请输入用户名" />
    </uni-forms-item>
  </uni-forms>
</template>
```

### ThorUI组件

项目集成了ThorUI组件库，通过easycom配置可直接使用，如：

```vue
<template>
  <tui-button type="primary">确认</tui-button>
</template>
```

### z-paging分页组件

用于列表分页加载：

```vue
<template>
  <z-paging ref="paging" v-model="dataList" @query="queryList">
    <!-- 列表内容 -->
  </z-paging>
</template>
```
