---
description: 
globs: 
alwaysApply: false
---
# 样式指南

本项目使用UnoCSS和SCSS进行样式管理。

## 颜色变量

颜色变量定义在[uni.scss](mdc:src/uni.scss)文件中：

- 主色调：`$primary`（橙色系列）
- 文本颜色：
  - `$text-main`: 主文本颜色 (#333)
  - `$text-info`: 信息文本颜色 (#666)
  - `$text-grey`: 灰色文本 (#999)
  - `$text-inverse`: 反色文本 (白色)
- 背景色：`$bg-color` (#f5f5f5)

## UnoCSS配置

项目使用UnoCSS进行原子化CSS开发，配置在[uno.config.ts](mdc:uno.config.ts)文件中。

### 自定义规则

项目中定义了许多自定义规则，包括：

- 文本颜色：`text-base`, `text-333`, `text-666`, `text-999`等
- 弹性布局：`flex-x`, `flex-y`, `flex-x-center`, `flex-x-between`等
- 字体大小：`ft12`, `ft14`, `ft16`等（对应24rpx, 28rpx, 32rpx）
- 间距：`spacing-4`, `spacing-8`, `spacing-12`等

### 使用方法

在组件中直接使用类名：

```vue
<template>
  <view class="flex-x-between p-4 bg-white">
    <text class="ft16 text-main font-bold">标题</text>
    <text class="ft14 text-grey">详情</text>
  </view>
</template>
```

## 安全区适配

项目支持安全区适配：

- `.safe-area-inset-bottom`: 底部安全区域填充
- `pt-safe`: 顶部安全区域填充
- `pb-safe`: 底部安全区域填充
