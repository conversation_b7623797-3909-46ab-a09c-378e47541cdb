---
description: 
globs: 
alwaysApply: false
---
# 状态管理

本项目使用[Pinia](mdc:https:/pinia.vuejs.org)进行状态管理，并通过`pinia-plugin-persistedstate`插件实现数据持久化。

## Store配置

主配置文件：[stores/index.ts](mdc:src/stores/index.ts)

此文件设置了Pinia实例并配置了持久化插件，使用uni-app的`uni.getStorageSync`和`uni.setStorageSync`作为存储方法。

## 用户状态

用户状态管理：[stores/user.ts](mdc:src/stores/user.ts)

用户Store包含：
- 状态：`user`和`accessToken`
- 操作：
  - `setUserInfo`: 设置用户信息和访问令牌
  - `clearUserInfo`: 清除用户信息
- Getter：
  - `getUser`: 获取用户信息

## 使用方法

在组件中使用Store：

```vue
<script setup lang="ts">
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 获取用户信息
const user = userStore.getUser

// 设置用户信息
function login(userData, token) {
  userStore.setUserInfo(userData, token)
}

// 退出登录
function logout() {
  userStore.clearUserInfo()
}
</script>
```
