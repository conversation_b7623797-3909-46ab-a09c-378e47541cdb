---
description: 
globs: 
alwaysApply: false
---
# 页面导航指南

本项目基于uni-app的页面路由系统，所有页面定义在[pages.json](mdc:src/pages.json)文件中。

## 主包页面

- **首页** - [pages/home/<USER>/pages/home/<USER>
- **社区** - [pages/post/list](mdc:src/pages/post/list.vue)
- **消息** - [pages/message/conversation](mdc:src/pages/message/conversation.vue)
- **我的** - [pages/mine/mine](mdc:src/pages/mine/mine.vue)

## 分包页面

### 招聘求职
- 职位列表 - `pages/job/index`
- 职位详情 - `pages/job/detail`
- 简历管理 - `pages/job/resume`
- 企业认证 - `pages/job/company-auth`

### 房产
- 二手房列表 - `pages/house/secondHouse/index`
- 租房列表 - `pages/house/rent/index`
- 房源详情 - `pages/house/detail/index`
- 商铺详情 - `pages/house/detail/commercial`

### 社交
- 相亲交友 - `pages/dating/index`

## TabBar配置

项目使用TabBar导航，包含四个主要入口：
- 首页
- 社区
- 消息
- 我的

TabBar配置位于[pages.json](mdc:src/pages.json)的`tabBar`部分。
