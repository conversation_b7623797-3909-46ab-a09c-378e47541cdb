---
description: 
globs: 
alwaysApply: false
---
# 表单与交互设计规范

本规范定义了项目中表单元素和用户交互的设计标准，旨在提供一致、直观的用户体验。

## 表单设计

### 表单布局

- 表单项采用垂直排列，标签在上、输入框在下
- 相关表单项分组展示，使用卡片或区域分隔
- 必填项使用红色星号(*)标识
- 保持左对齐，提高可扫读性

```vue
<template>
  <uni-forms :model="form" :rules="rules">
    <view class="form-group spacing-16">
      <view class="form-title ft16 text-main font-bold spacing-8">基本信息</view>
      <uni-forms-item label="用户名" required>
        <uni-easyinput v-model="form.username" placeholder="请输入用户名" />
      </uni-forms-item>
      <uni-forms-item label="手机号" required>
        <uni-easyinput v-model="form.phone" placeholder="请输入手机号" />
      </uni-forms-item>
    </view>
  </uni-forms>
</template>
```

### 输入控件

- 使用`uni-easyinput`或`tui-input`组件
- 输入框高度统一: 80rpx (40px)
- 提供明确的输入提示(placeholder)
- 输入框内左右内边距: 24rpx (12px)

### 表单验证与反馈

- 实时验证: 用户输入完成后立即验证
- 错误提示: 在输入框下方显示简洁明了的错误信息
- 使用红色(`$text-red`)标识错误
- 成功提交后显示明确的成功反馈

## 按钮设计

### 按钮类型与层级

- **主要按钮**: 使用主色调(`$primary`)，突出最重要的操作
- **次要按钮**: 使用白底+边框或浅色背景，用于次要操作
- **文本按钮**: 仅使用文本，用于辅助操作

```vue
<template>
  <view class="btn-group flex-x-between py-4">
    <tui-button type="white" size="small">取消</tui-button>
    <tui-button type="primary" size="small">确认</tui-button>
  </view>
</template>
```

### 按钮状态

- 正常态: 默认样式
- 悬浮态: 亮度轻微提高
- 点击态: 亮度轻微降低
- 禁用态: 使用灰色且不可点击

### 按钮大小与位置

- 全宽按钮: 用于表单底部的主操作按钮
- 固定底部: 重要操作按钮固定在页面底部
- 大小尺寸: 根据内容和重要性选择合适尺寸(small/medium/large)

## 交互反馈

### 加载状态

- 全局加载: 使用`uni.showLoading()`
- 局部加载: 使用`<tui-loading>`或`<uni-load-more>`
- 按钮加载: 按钮内显示加载图标

```vue
<template>
  <tui-button type="primary" :loading="isLoading" @click="submit">
    {{ isLoading ? '提交中' : '提交' }}
  </tui-button>
</template>
```

### 消息提示

- 成功提示: `uni.showToast({ title: '操作成功', icon: 'success' })`
- 错误提示: `uni.showToast({ title: '操作失败', icon: 'none' })`
- 警告确认: 使用`uni.showModal()`进行二次确认

### 手势操作

- 下拉刷新: 用于刷新列表数据
- 上拉加载: 用于分页加载更多数据
- 左滑操作: 显示附加操作(如删除)

## 列表与数据展示

### 空状态设计

- 提供友好的空状态提示
- 使用插图+文字说明
- 必要时提供操作引导

```vue
<template>
  <view v-if="list.length === 0" class="empty-state flex-y-center py-20">
    <image src="/static/images/empty.png" mode="aspectFit" class="empty-image" />
    <text class="ft14 text-grey mt-2">暂无数据</text>
    <tui-button type="primary" size="small" class="mt-4">添加数据</tui-button>
  </view>
</template>
```

### 列表交互

- 使用`z-paging`组件实现下拉刷新和上拉加载
- 点击项目时提供视觉反馈
- 列表项保持一致的高度和布局结构

## 动效使用

- 简单过渡: `transition: all 0.3s ease`
- 页面切换: 使用`uni.navigateTo`的动画效果
- 谨慎使用动画，确保不影响性能

以上规范帮助创建直观、易用且统一的用户界面，提升整体用户体验。